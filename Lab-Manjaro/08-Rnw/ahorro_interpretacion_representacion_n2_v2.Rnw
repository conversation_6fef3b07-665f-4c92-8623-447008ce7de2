\documentclass[a4paper]{article}

\usepackage[utf8]{inputenc}
\usepackage[spanish]{babel}
\usepackage{amsmath,amssymb,amsfonts}
\usepackage{graphicx}
\usepackage{enumitem}

% Definir los entornos necesarios para exams
\newenvironment{question}{}{}
\newenvironment{solution}{}{}
\newenvironment{answerlist}{\begin{enumerate}}{\end{enumerate}}

\begin{document}
\SweaveOpts{concordance=TRUE}

<<echo=FALSE, results=hide>>=
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# CONFIGURACIÓN RADICAL ANTI-NOTACIÓN CIENTÍFICA
options(scipen = 999)  # Evitar notación científica completamente
options(digits = 10)   # Suficientes dígitos para números grandes

library(exams)

# Función para formatear enteros sin notación científica
formatear_entero <- function(numero) {
  # Forzar formato entero sin notación científica JAMÁS
  formatC(as.numeric(numero), format = "d", big.mark = "")
}

# Función para formatear números monetarios
formatear_monetario <- function(numero) {
  # Formatear con separador de miles punto y sin notación científica
  formatC(as.numeric(numero), format = "d", big.mark = ".", decimal.mark = ",")
}

# Generar datos aleatorios
ok <- FALSE
while(!ok) {
  # Datos basicos
  nombres <- c("Ana", "Carlos", "Maria", "Diego", "Sofia", "Andres")

  # Seleccionar aleatoriamente
  nombre <- sample(nombres, 1)
  familiar1 <- "tio"
  familiar2 <- "tia"

  # Parametros del problema
  ahorro_mensual <- sample(seq(125000, 200000, 25000), 1)
  meses <- 3  # Simplificar a 3 meses

  # Porcentajes
  porcentaje_constante <- sample(8:12, 1)
  porcentajes_variables <- c(
    sample(1:5, 1),    # Mes 1: bajo
    sample(3:7, 1),    # Mes 2: medio
    sample(15:25, 1)   # Mes 3: alto
  )

  # Calcular ahorros acumulados
  ahorros_acumulados <- ahorro_mensual * (1:meses)

  # Calcular regalos para cada opcion
  regalos_opcion1 <- ahorros_acumulados * porcentaje_constante / 100
  regalos_opcion2 <- ahorros_acumulados * porcentajes_variables / 100

  # Totales
  total_opcion1 <- sum(regalos_opcion1)
  total_opcion2 <- sum(regalos_opcion2)

  # Verificar que hay diferencia significativa
  diferencia <- abs(total_opcion1 - total_opcion2)
  if(diferencia > 10000) {
    ok <- TRUE
  }
}

# Determinar cual opcion es mejor
opcion_mejor <- ifelse(total_opcion1 > total_opcion2, 1, 2)

# Crear opciones de respuesta argumentadas basadas en la elección del personaje
opciones <- character(4)

# Determinar quién eligió el personaje (la opción con mayor total)
if(total_opcion2 > total_opcion1) {
  # El personaje eligió a la tía (opción 2) - ES CORRECTA
  opciones[1] <- paste("Si, porque la ayuda total de la", familiar2, "es de \\$", formatear_monetario(total_opcion2),
                       "mientras que la del", familiar1, "es de \\$", formatear_monetario(total_opcion1), ".")
  opciones[2] <- paste("No, porque la ayuda total del", familiar1, "es de \\$", formatear_monetario(total_opcion1),
                       "mientras que la de la", familiar2, "es de \\$", formatear_monetario(total_opcion2), ".")
  opciones[3] <- paste("Si, porque con la ayuda de la", familiar2, "recibe el", round(sum(porcentajes_variables)/3, 1),
                       "\\% del total ahorrado y con la ayuda del", familiar1, "recibe el", porcentaje_constante, "\\%.")
  opciones[4] <- paste("No, porque con la ayuda de la", familiar2, "el porcentaje del primer mes es del", porcentajes_variables[1],
                       "\\% y con la ayuda del", familiar1, "es del", porcentaje_constante, "\\%.")
} else {
  # El personaje eligió al tío (opción 1) - ES CORRECTA
  opciones[1] <- paste("Si, porque la ayuda total del", familiar1, "es de \\$", formatear_monetario(total_opcion1),
                       "mientras que la de la", familiar2, "es de \\$", formatear_monetario(total_opcion2), ".")
  opciones[2] <- paste("No, porque la ayuda total de la", familiar2, "es de \\$", formatear_monetario(total_opcion2),
                       "mientras que la del", familiar1, "es de \\$", formatear_monetario(total_opcion1), ".")
  opciones[3] <- paste("Si, porque con la ayuda del", familiar1, "recibe el", porcentaje_constante,
                       "\\% del total ahorrado y con la ayuda de la", familiar2, "recibe el", round(sum(porcentajes_variables)/3, 1), "\\%.")
  opciones[4] <- paste("No, porque con la ayuda del", familiar1, "el porcentaje es constante del", porcentaje_constante,
                       "\\% y con la ayuda de la", familiar2, "el tercer mes es del", porcentajes_variables[3], "\\%.")
}

# Determinar respuesta correcta
# Como el personaje siempre elige la opción correcta (la que da más dinero),
# la respuesta siempre será "Sí" (opción 1)
solucion <- c(TRUE, FALSE, FALSE, FALSE)

# Crear explicaciones detalladas
explicaciones <- character(4)

if(total_opcion2 > total_opcion1) {
  # La tía da más dinero, entonces el personaje eligió correctamente a la tía
  explicaciones[1] <- paste("Correcto. Al calcular los totales: ", familiar2, " = \\$", formatear_monetario(total_opcion2),
                           " y ", familiar1, " = \\$", formatear_monetario(total_opcion1), ". La ", familiar2, " ofrece mas ayuda.")
  explicaciones[2] <- paste("Incorrecto. Al calcular los totales: ", familiar1, " = \\$", formatear_monetario(total_opcion1),
                           " y ", familiar2, " = \\$", formatear_monetario(total_opcion2), ". El ", familiar1, " no ofrece mas ayuda.")
  explicaciones[3] <- paste("Incorrecto. Aunque el porcentaje promedio puede parecer similar, lo importante son los totales absolutos: \\$",
                           formatear_monetario(total_opcion2), " vs \\$", formatear_monetario(total_opcion1), ".")
  explicaciones[4] <- paste("Incorrecto. Aunque algunos porcentajes individuales pueden ser menores, el total acumulado de la ",
                           familiar2, " (\\$", formatear_monetario(total_opcion2), ") es mayor.")
} else {
  # El tío da más dinero, entonces el personaje eligió correctamente al tío
  explicaciones[1] <- paste("Correcto. Al calcular los totales: ", familiar1, " = \\$", formatear_monetario(total_opcion1),
                           " y ", familiar2, " = \\$", formatear_monetario(total_opcion2), ". El ", familiar1, " ofrece mas ayuda.")
  explicaciones[2] <- paste("Incorrecto. Al calcular los totales: ", familiar2, " = \\$", formatear_monetario(total_opcion2),
                           " y ", familiar1, " = \\$", formatear_monetario(total_opcion1), ". La ", familiar2, " no ofrece mas ayuda.")
  explicaciones[3] <- paste("Incorrecto. Aunque el porcentaje constante puede parecer menor, lo importante son los totales absolutos: \\$",
                           formatear_monetario(total_opcion1), " vs \\$", formatear_monetario(total_opcion2), ".")
  explicaciones[4] <- paste("Incorrecto. Aunque algunos porcentajes individuales pueden ser mayores, el total acumulado del ",
                           familiar1, " (\\$", formatear_monetario(total_opcion1), ") es mayor.")
}
@

\begin{question}
\Sexpr{nombre} quiere ahorrar \$\Sexpr{formatear_monetario(ahorro_mensual)} cada mes durante \Sexpr{meses} meses. Como ayuda para su proyecto, sus padres le han propuesto dos opciones, pero solo puede elegir una de ellas.

\textbf{Opcion 1 (tio):} Al finalizar cada mes, su tio le regala un porcentaje del dinero que tenga acumulado.

\begin{center}
\begin{tabular}{|c|c|c|}
\hline
\textbf{Ahorro Acumulado} & \textbf{Mes} & \textbf{Porcentaje regalado} \\
\hline
\$\Sexpr{formatear_monetario(ahorros_acumulados[1])} & 1 & \Sexpr{porcentaje_constante}\% \\
\hline
\$\Sexpr{formatear_monetario(ahorros_acumulados[2])} & 2 & \Sexpr{porcentaje_constante}\% \\
\hline
\$\Sexpr{formatear_monetario(ahorros_acumulados[3])} & 3 & \Sexpr{porcentaje_constante}\% \\
\hline
\end{tabular}
\end{center}

\textbf{Opcion 2 (tia):} Al finalizar cada mes, su tia le regala un porcentaje del dinero que tenga acumulado.

\begin{center}
\begin{tabular}{|c|c|c|}
\hline
\textbf{Ahorro Acumulado} & \textbf{Mes} & \textbf{Porcentaje regalado} \\
\hline
\$\Sexpr{formatear_monetario(ahorros_acumulados[1])} & 1 & \Sexpr{porcentajes_variables[1]}\% \\
\hline
\$\Sexpr{formatear_monetario(ahorros_acumulados[2])} & 2 & \Sexpr{porcentajes_variables[2]}\% \\
\hline
\$\Sexpr{formatear_monetario(ahorros_acumulados[3])} & 3 & \Sexpr{porcentajes_variables[3]}\% \\
\hline
\end{tabular}
\end{center}

\Sexpr{nombre} decide elegir la opcion en la que le regalen la mayor cantidad de dinero y elige la ayuda \Sexpr{if(total_opcion2 > total_opcion1) paste("de la", familiar2) else paste("del", familiar1)}. ¿Es correcta la eleccion de \Sexpr{nombre}?

<<echo=FALSE, results=tex>>=
answerlist(opciones)
@
\end{question}

\begin{solution}
Para resolver este problema, debemos calcular la ayuda total que recibiria \Sexpr{nombre} con cada opcion.

\textbf{Opcion 1 (tio):}

Mes 1: \$\Sexpr{formatear_monetario(ahorros_acumulados[1])} $\times$ \Sexpr{porcentaje_constante}\% = \$\Sexpr{formatear_monetario(regalos_opcion1[1])}

Mes 2: \$\Sexpr{formatear_monetario(ahorros_acumulados[2])} $\times$ \Sexpr{porcentaje_constante}\% = \$\Sexpr{formatear_monetario(regalos_opcion1[2])}

Mes 3: \$\Sexpr{formatear_monetario(ahorros_acumulados[3])} $\times$ \Sexpr{porcentaje_constante}\% = \$\Sexpr{formatear_monetario(regalos_opcion1[3])}

Total tio: \$\Sexpr{formatear_monetario(total_opcion1)}

\textbf{Opcion 2 (tia):}

Mes 1: \$\Sexpr{formatear_monetario(ahorros_acumulados[1])} $\times$ \Sexpr{porcentajes_variables[1]}\% = \$\Sexpr{formatear_monetario(regalos_opcion2[1])}

Mes 2: \$\Sexpr{formatear_monetario(ahorros_acumulados[2])} $\times$ \Sexpr{porcentajes_variables[2]}\% = \$\Sexpr{formatear_monetario(regalos_opcion2[2])}

Mes 3: \$\Sexpr{formatear_monetario(ahorros_acumulados[3])} $\times$ \Sexpr{porcentajes_variables[3]}\% = \$\Sexpr{formatear_monetario(regalos_opcion2[3])}

Total tia: \$\Sexpr{formatear_monetario(total_opcion2)}

Por lo tanto, la respuesta correcta es que \Sexpr{if(opcion_mejor == 1) "el tio" else "la tia"} ofrece mas ayuda.

<<echo=FALSE, results=tex>>=
answerlist(explicaciones)
@
\end{solution}

%% META-INFORMATION
%% \extype{schoice}
%% \exsolution{\Sexpr{mchoice2string(solucion)}}
%% \exname{Ahorro con porcentajes}

\end{document}
